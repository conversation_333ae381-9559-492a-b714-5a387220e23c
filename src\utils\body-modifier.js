/**
 * 修改请求body中的模型ID
 * @param {string} bodyStr 原始body字符串
 * @param {string} model 目标模型名称
 * @returns {string} 修改后的body字符串
 */
export function modifyBodyForModel(bodyStr, model) {

  console.log('[modifyBodyForModel] 收到原始body:', bodyStr);

  return bodyStr;

  try {
    const body = JSON.parse(bodyStr);

    return JSON.stringify(body);
  } catch (err) {
    console.error('[modifyBodyForModel] JSON解析失败:', err);
    return bodyStr;
  }
}
