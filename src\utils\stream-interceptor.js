import { info, error } from './logger.js'
import config from '../config.js'
import { modifyBodyForModel } from './body-modifier.js'
import { compatEvaluate } from './browser.js'

// 根据这个 Patchright API 的文档，`isolatedContext` 参数用于选择 JavaScript 代码的执行上下文（Execution Context）。

// ## isolatedContext 参数的含义

// `isolatedContext` 是一个布尔值参数，用于控制代码在哪个执行上下文中运行：

// - **`true`（默认值）**：代码在**隔离上下文（Isolated Context）**中执行
// - **`false`**：代码在**主上下文（Main Context）**中执行

// ## 两种执行上下文的区别

// ### 隔离上下文（Isolated Context）
// - 这是一个独立的 JavaScript 环境
// - 与页面的原始 JavaScript 环境隔离
// - 不会受到页面自身 JavaScript 代码的影响
// - 更安全，避免与页面代码产生冲突
// - 适合执行自动化测试代码

// ### 主上下文（Main Context）
// - 这是页面的原始 JavaScript 环境
// - 可以访问页面定义的全局变量和函数
// - 与页面的 JavaScript 代码共享同一个执行环境
// - 可能会受到页面代码的影响
// - 适合需要与页面代码交互的场景

// ## 使用示例

// ```javascript
// // 在隔离上下文中执行（默认）
// await page.evaluate(() => {
//     return document.title;
// }, undefined, true);

// // 在主上下文中执行
// await page.evaluate(() => {
//     return window.myGlobalVariable; // 访问页面的全局变量
// }, undefined, false);
// ```

// 选择哪种上下文取决于你的具体需求：如果需要与页面代码交互，使用主上下文；如果只是进行数据提取或DOM操作，建议使用隔离上下文以避免潜在的冲突。


/**
 * 封装了对 页面网络请求的流式拦截逻辑。
 * 
 *  注意为了兼容patchright所有的evaluate都有三个参数.最后一个是false,在主上下文中执行
 *  await this.page.evaluate(browserScript, injectionPayload,false);
 */
export class StreamInterceptor {
  /** @readonly */
  TARGET_URL_PATTERNS = config.targetUrlPatterns;

  // END_OF_STREAM_SIGNAL 已被移除
  /**
   * @param {import('playwright').Page} page Playwright 页面对象
   * @param {function(string): void} onStreamChunk 流数据块回调
   * @param {function(): void} onStreamEnd 流结束回调
   * @param {Object} opts 选项参数
   * @param {string} opts.model 模型名称，用于替换请求中的modelAId
   */
  constructor(page, onStreamChunk, onStreamEnd, opts = {}) {
    this.page = page;
    this.onStreamChunk = onStreamChunk;
    this.onStreamEnd = onStreamEnd;
    this.opts = opts;
    this.uniqueId = `interceptor_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    this.chunkCallbackName = `__onStreamChunk_${this.uniqueId}`;
    this.endCallbackName = `__onStreamEnd_${this.uniqueId}`;
    this.modifyBodyCallbackName = `__modifyBody_${this.uniqueId}`;
    this.isInjected = false;
    this.isActive = false;
  }

  /**
   * 将拦截器脚本和所有配置一次性注入到页面中。
   * @private
   */
  async _inject() {
    if (this.isInjected) return;

    info(`[Interceptor] 注入流式拦截脚本 (ID: ${this.uniqueId})...`);
    await this.page.exposeFunction(this.chunkCallbackName, this.onStreamChunk);
    await this.page.exposeFunction(this.endCallbackName, this.onStreamEnd);

    // 暴露修改body的函数
    await this.page.exposeFunction(this.modifyBodyCallbackName, (bodyStr) => {
      return modifyBodyForModel(bodyStr, this.opts.model);
    });

    const injectionPayload = {
      TARGET_URL_PATTERNS: this.TARGET_URL_PATTERNS,
      chunkCallbackName: this.chunkCallbackName,
      endCallbackName: this.endCallbackName,
      modifyBodyCallbackName: this.modifyBodyCallbackName,
    };

    const browserScript = this._getBrowserScript();
    await compatEvaluate(this.page,browserScript, injectionPayload,false);

    this.isInjected = true;
    info(`[Interceptor] 脚本注入和回调连接已一次性完成 (ID: ${this.uniqueId})`);
  }

  // ... activate 和 deactivate 方法保持不变 ...
  async activate() {
    if (this.isActive) return;
    await this._inject();

    info(`[Interceptor] 激活拦截器 (ID: ${this.uniqueId})...`);
    await compatEvaluate(this.page,() => {
      if (window.__streamInterceptor) {
        window.__streamInterceptor.activate();
      }
    },undefined, false);
    this.isActive = true;
    info(`[Interceptor] 拦截器已激活 (ID: ${this.uniqueId})`);
  }

  async deactivate() {
    if (!this.isActive) return;

    info(`[Interceptor] 停用拦截器 (ID: ${this.uniqueId})...`);
    try {
      await compatEvaluate(this.page,() => {
        if (window.__streamInterceptor) {
          window.__streamInterceptor.deactivate();
        }
      },undefined, false);
      this.isActive = false;
      info(`[Interceptor] 拦截器已停用 (ID: ${this.uniqueId})`);
    } catch (err) {
      error(`[Interceptor] 停用时发生错误 (可能页面已关闭): ${err.message}`);
    }
  }

  /**
   * 返回要注入到浏览器页面的脚本函数。
   * 这是最直接的版本，采用纯事件驱动模型。
   * @returns {function}
   * @private
   */
  _getBrowserScript() {
    return (payload) => {
      if (window.__streamInterceptor) {
        if (typeof window.__streamInterceptor.deactivate === 'function') {
          window.__streamInterceptor.deactivate();
        }
      }

      const {
        TARGET_URL_PATTERNS,
        chunkCallbackName,
        endCallbackName,
        modifyBodyCallbackName,
      } = payload;

 
      const originalFetch = window.fetch;

      const onChunkCallback = window[chunkCallbackName];
      const onEndCallback = window[endCallbackName];
      const modifyBodyCallback = window[modifyBodyCallbackName];
      console.log(`[Interceptor] 回调已在注入时直接获取: ${chunkCallbackName}, ${endCallbackName}, ${modifyBodyCallbackName}`);

      let interceptorActive = false;

      window.__streamInterceptor = {
        activate: () => {
          if (interceptorActive) return;
          console.log('🎯 [Interceptor] 激活 Fetch 拦截器...');
          interceptorActive = true;

          // 拦截 fetch API
          window.fetch = async function(input, init = {}) {
            const url = typeof input === 'string' ? input : input.url;
            const urlString = url ? url.toString() : '';
            const isTargetRequest = TARGET_URL_PATTERNS.some(pattern => urlString.includes(pattern));
            if (isTargetRequest && interceptorActive) {
              console.log('🎯 [Interceptor] 拦截到目标 fetch 请求:', urlString);

              try {
                // 修改请求body中的模型ID
                if (init && init.body && modifyBodyCallback) {
                  const originalBody = init.body;
                  const modifiedBody = await modifyBodyCallback(originalBody);
                  if (modifiedBody !== originalBody) {
                    console.log('🔄 [Interceptor] 已修改请求body中的模型ID');
                    init = { ...init, body: modifiedBody };
                  }
                }

                const response = await originalFetch.call(this, input, init);

                if (!response.body) {
                  console.warn('[Interceptor] 响应没有 body，无法处理流');
                  return response;
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullResponseText = '';
                let streamEnded = false;

                const finalizeStream = () => {
                  if (streamEnded) return;
                  streamEnded = true;
                  console.log('🏁 [Interceptor] Fetch 流已结束，触发最终回调...');
                  if (onEndCallback) {
                    onEndCallback();
                  }
                };

                // 创建一个新的 ReadableStream 来代理原始流
                const stream = new ReadableStream({
                  start(controller) {
                    function pump() {
                      return reader.read().then(({ done, value }) => {
                        if (done) {
                          console.log('🏁 [Interceptor] Fetch 流读取完成');
                          finalizeStream();
                          controller.close();
                          return;
                        }

                        const chunk = decoder.decode(value, { stream: true });
                        fullResponseText += chunk;

                        // 发送数据块回调
                        if (onChunkCallback) {
                          onChunkCallback(chunk);
                        }

                        controller.enqueue(value);
                        return pump();
                      });
                    }

                    return pump();
                  }
                });

                // 返回一个新的 Response 对象，使用我们的代理流
                return new Response(stream, {
                  status: response.status,
                  statusText: response.statusText,
                  headers: response.headers
                });

              } catch (error) {
                console.error('[Interceptor] Fetch 拦截出错:', error);
                return originalFetch.call(this, input, init);
              }
            }

            return originalFetch.call(this, input, init);
          };
        },

        deactivate: () => {
          if (!interceptorActive) return;
          console.log('🔄 [Interceptor] 停用 Fetch 拦截器...');
          window.fetch = originalFetch;
          interceptorActive = false;
        }
      };
    };
  }
}